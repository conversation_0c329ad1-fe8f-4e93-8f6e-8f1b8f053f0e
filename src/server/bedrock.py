# # src/server/bedrock.py
# import json
# import boto3
# import logging
# import time
# import re
# from typing import Dict, Any, Optional
# from botocore.exceptions import ClientError

# # Configure standard Python logging
# logger = logging.getLogger()
# logger.setLevel(logging.INFO)

# class BedrockClient:
#     def __init__(self, region_name: str = 'ap-south-1'):
#         self.client = boto3.client('bedrock-runtime', region_name=region_name)
#         # Token limits for different models (approximate)
#         self.token_limits = {
#             'mistral.mistral-large-2402-v1:0': 32000,  
#             'anthropic.claude-3-sonnet-20240229-v1:0': 200000,
#             'anthropic.claude-3-haiku-20240307-v1:0': 200000,
#         }
    
#     def estimate_tokens(self, text: str) -> int:
#         """Rough token estimation (1 token ≈ 4 characters for most models)"""
#         return len(text) // 4
    
#     def truncate_text(self, text: str, model_id: str, reserve_tokens: int = 1000) -> str:
#         """Truncate text to fit within model token limits"""
#         max_tokens = self.token_limits.get(model_id, 32000) - reserve_tokens
#         estimated_tokens = self.estimate_tokens(text)
#         print("estimated_tokens::", estimated_tokens)
        
#         if estimated_tokens <= max_tokens:
#             return text
        
#         # Calculate how much text to keep (roughly)
#         ratio = max_tokens / estimated_tokens
#         target_length = int(len(text) * ratio * 0.9)  # Use 90% to be safe
        
#         # Try to cut at sentence boundaries
#         truncated = text[:target_length]
#         last_sentence = truncated.rfind('.')
#         if last_sentence > target_length * 0.8:  # If we found a good sentence break
#             truncated = truncated[:last_sentence + 1]
        
#         logger.warning(f"Text truncated from {len(text)} to {len(truncated)} characters due to token limits")
#         return truncated + "\n\n[TEXT TRUNCATED DUE TO LENGTH]"
    
#     def invoke_model_with_retry(self, model_id: str, payload: Dict[str, Any], max_retries: int = 3) -> Any:
#         """
#         Invoke a Bedrock model with exponential backoff retry logic.
#         """
#         for attempt in range(max_retries + 1):
#             try:
#                 return self._invoke_model_once(model_id, payload)
            
#             except ClientError as e:
#                 error_code = e.response['Error']['Code']
                
#                 if error_code == 'ThrottlingException':
#                     if attempt < max_retries:
#                         # Exponential backoff: 2^attempt seconds
#                         wait_time = 2 ** attempt
#                         logger.warning(f"Throttling detected, waiting {wait_time}s before retry {attempt + 1}/{max_retries}")
#                         time.sleep(wait_time)
#                         continue
#                     else:
#                         logger.error("Max retries reached for throttling")
#                         return {"error": "Rate limit exceeded - please try again later"}
                
#                 elif error_code == 'ValidationException':
#                     if 'too many tokens' in str(e).lower():
#                         logger.warning("Token limit exceeded, truncating text and retrying")
#                         # Truncate the text in the payload
#                         if 'body' in payload and 'messages' in payload['body']:
#                             for message in payload['body']['messages']:
#                                 if message['role'] == 'user':
#                                     message['content'] = self.truncate_text(message['content'], model_id)
#                         # Retry with truncated text
#                         if attempt < max_retries:
#                             continue
                    
#                     logger.error(f"Validation error: {str(e)}")
#                     return {"error": f"Validation error: {str(e)}"}
                
#                 else:
#                     logger.error(f"Bedrock error: {str(e)}")
#                     return {"error": f"Bedrock error: {str(e)}"}
            
#             except Exception as e:
#                 logger.error(f"Unexpected error: {str(e)}")
#                 if attempt < max_retries:
#                     time.sleep(1)
#                     continue
#                 return {"error": f"Unexpected error: {str(e)}"}
        
#         return {"error": "Max retries exceeded"}
    
#     def _invoke_model_once(self, model_id: str, payload: Dict[str, Any]) -> Any:
#         """Single model invocation attempt"""
#         if 'body' not in payload:
#             raise ValueError("Payload must contain 'body'")
        
#         accept = payload.get('accept', 'application/json')
#         content_type = payload.get('contentType', 'application/json')
        
#         # Log the request size for debugging
#         body_str = json.dumps(payload['body'])
#         estimated_tokens = self.estimate_tokens(body_str)
#         logger.info(f"Sending request to {model_id}: ~{estimated_tokens} tokens")
        
#         response = self.client.invoke_model(
#             modelId=model_id,
#             body=body_str,
#             contentType=content_type,
#             accept=accept
#         )
        
#         response_body = json.loads(response['body'].read().decode('utf-8'))
        
#         # Handle different response formats
#         if 'choices' in response_body:  # OpenAI-style response
#             return response_body['choices'][0]['message']['content']
#         elif 'completion' in response_body:  # Text completion
#             return response_body['completion']
#         elif 'outputs' in response_body:  # Some models use 'outputs'
#             return response_body['outputs'][0]['text']
#         elif isinstance(response_body, dict) and 'text' in response_body:
#             return response_body['text']
#         else:
#             # Return the full response if we can't parse it
#             logger.warning(f"Unknown response format: {response_body}")
#             return response_body
    
#     def invoke_model(self, model_id: str, payload: Dict[str, Any]) -> Any:
#         """
#         Main entry point for model invocation with all safety features.
#         """
#         try:
#             # Pre-check for obvious token limit issues
#             if 'body' in payload and 'messages' in payload['body']:
#                 total_length = sum(len(msg.get('content', '')) for msg in payload['body']['messages'])
#                 estimated_tokens = self.estimate_tokens(str(total_length))
                
#                 if estimated_tokens > self.token_limits.get(model_id, 32000):
#                     logger.warning("Pre-truncating text due to estimated token limit")
#                     for message in payload['body']['messages']:
#                         if message['role'] == 'user' and len(message.get('content', '')) > 10000:
#                             message['content'] = self.truncate_text(message['content'], model_id)
            
#             result = self.invoke_model_with_retry(model_id, payload)
            
#             # Ensure we return a string for successful responses
#             if isinstance(result, dict) and 'error' in result:
#                 return result  # Return error dict as-is
#             elif isinstance(result, str):
#                 return result  # Return string response
#             else:
#                 # Try to extract text from complex responses
#                 if isinstance(result, dict):
#                     return str(result)  # Convert to string as fallback
#                 else:
#                     return str(result)
        
#         except Exception as e:
#             logger.error(f"[BedrockClient] Error invoking Bedrock model: {str(e)}")
#             return {"error": str(e)}

# # Singleton instance
# bedrock_client = BedrockClient()

# # Legacy-style function for compatibility
# def bedrockInovke(model_id: str, payload: Dict[str, Any]) -> Any:
#     return bedrock_client.invoke_model(model_id, payload)



















# src/server/bedrock.py

# import json
# import boto3
# import logging
# from typing import Dict, Any

# # Configure standard Python logging (FastAPI's logger is not available in Lambda)
# logger = logging.getLogger()
# logger.setLevel(logging.INFO)

# class BedrockClient:
#     def __init__(self, region_name: str = 'ap-south-1'):
#         self.client = boto3.client('bedrock-runtime', region_name=region_name)

#     def invoke_model(self, model_id: str, payload: Dict[str, Any]) -> Any:
#         """
#         Invoke a Bedrock model with the given payload.

#         Args:
#             model_id: The ID of the Bedrock model to invoke
#             payload: The input payload for the model

#         Returns:
#             The parsed response from the model (typically a string or dict)
#         """
#         try:
#             if 'body' not in payload:
#                 raise ValueError("Payload must contain 'body'")

#             accept = payload.get('accept', 'application/json')
#             content_type = payload.get('contentType', 'application/json')

#             response = self.client.invoke_model(
#                 modelId=model_id,
#                 body=json.dumps(payload['body']),
#                 contentType=content_type,
#                 accept=accept
#             )

#             response_body = json.loads(response['body'].read().decode('utf-8'))

#             # Bedrock response may vary depending on model type
#             if 'choices' in response_body:  # Chat response
#                 return response_body['choices'][0]['message']['content']
#             elif 'completion' in response_body:  # Text completion
#                 return response_body['completion']
#             else:
#                 return response_body

#         except Exception as e:
#             logger.error(f"[BedrockClient] Error invoking Bedrock model: {str(e)}")
#             return {}

# # Singleton instance
# bedrock_client = BedrockClient()

# # Legacy-style function for compatibility
# def bedrockInovke(model_id: str, payload: Dict[str, Any]) -> Any:
#     return bedrock_client.invoke_model(model_id, payload)



import json
import boto3
import logging
import time
import random
from typing import Dict, Any

# Configure standard logging
logger = logging.getLogger()
logger.setLevel(logging.INFO)

class BedrockClient:
    def __init__(self, region_name: str = 'ap-south-1'):
        self.client = boto3.client('bedrock-runtime', region_name=region_name)

    def invoke_model(self, model_id: str, payload: Dict[str, Any], max_retries: int = 5) -> Any:
        if 'body' not in payload:
            raise ValueError("Payload must contain 'body'")
        
        accept = payload.get('accept', 'application/json')
        content_type = payload.get('contentType', 'application/json')

        for attempt in range(1, max_retries + 1):
            try:
                response = self.client.invoke_model(
                    modelId=model_id,
                    body=json.dumps(payload['body']),
                    contentType=content_type,
                    accept=accept
                )

                response_body = json.loads(response['body'].read().decode('utf-8'))

                if 'choices' in response_body:
                    return response_body['choices'][0]['message']['content']
                elif 'completion' in response_body:
                    return response_body['completion']
                else:
                    return response_body

            except self.client.exceptions.ThrottlingException as e:
                wait_time = min(2 ** attempt + random.uniform(0, 1), 10)  # backoff + jitter
                logger.warning(f"[BedrockClient] Attempt {attempt} failed due to throttling. Retrying in {wait_time:.2f}s...")
                time.sleep(wait_time)
            except Exception as e:
                logger.error(f"[BedrockClient] Error invoking Bedrock model: {str(e)}")
                break  # other errors don't retry

        logger.error(f"[BedrockClient] All {max_retries} attempts failed.")
        return {}

# Singleton instance
bedrock_client = BedrockClient()

def bedrockInovke(model_id: str, payload: Dict[str, Any]) -> Any:
    return bedrock_client.invoke_model(model_id, payload)
