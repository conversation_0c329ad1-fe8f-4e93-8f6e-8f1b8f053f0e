# src/server/model_check.py

import json
import logging
import boto3
from botocore.exceptions import ClientError

# Standard Python logging (good for Lambda)
logger = logging.getLogger()
logger.setLevel(logging.INFO)

def list_foundation_models(bedrock_client):
    """
    Gets a list of available Amazon Bedrock foundation models.
    """
    try:
        response = bedrock_client.list_foundation_models()
        models = response.get("modelSummaries", [])
        logger.info("Got %s foundation models.", len(models))
        return models
    except ClientError as e:
        logger.error(f"Couldn't list foundation models: {e}")
        raise

def modelCheck():
    """
    Entry point to list available Bedrock models.
    Automatically uses region and credentials from environment.
    """
    try:
        bedrock_client = boto3.client("bedrock", region_name="ap-south-1")
        return list_foundation_models(bedrock_client)
    except Exception as e:
        logger.error(f"modelCheck failed: {e}")
        return []
