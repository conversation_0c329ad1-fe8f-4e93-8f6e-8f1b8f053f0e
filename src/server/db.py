import os
import boto3
import json
import psycopg2
from botocore.exceptions import ClientError

SECRET_NAME = os.environ["SECRET_NAME"]
REGION_NAME = os.environ.get("AWS_REGION", "ap-south-1")

def get_db_credentials(secret_name: str, region_name: str = "ap-south-1"):
    """Fetch DB credentials from AWS Secrets Manager."""
    session = boto3.session.Session()
    client = session.client(service_name='secretsmanager', region_name=region_name)

    try:
        response = client.get_secret_value(SecretId=secret_name)
        secret = response.get('SecretString')
        creds = json.loads(secret)

        # Fix AWS format to match psycopg2 keys
        db_config = {
            'host': creds['host'],
            'port': creds['port'],
            'user': creds.get('username') or creds.get('user'),
            'password': creds['password'],
            'dbname': creds.get('dbname') or creds.get('database')
        }

        print(f"🔐 Loaded DB user: {db_config['user']}")
        print(f"🔐 Loaded password: '{db_config['password']}'")

        return db_config
    except ClientError as e:
        print(f"❌ Error fetching secret: {e}")
        return None

def get_db_connection():
    """Returns a PostgreSQL connection and cursor."""
    db_config = get_db_credentials(SECRET_NAME, REGION_NAME)
    if not db_config:
        return None, None

    try:
        conn = psycopg2.connect(**db_config)
        cursor = conn.cursor()
        return conn, cursor
    except Exception as e:
        print(f"❌ Failed to connect to DB: {e}")
        return None, None
