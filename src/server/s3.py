# src/server/s3_client.py

import os
import boto3
from typing import Optional

class S3Client:
    def __init__(self):
        # Use IAM role for credentials (recommended for Lambda)
        self.client = boto3.client("s3")
        self.bucket = os.getenv("S3_BUCKET_NAME")

    def get_document(self, file_name: str, local_dir: str = "/tmp") -> Optional[str]:
        """
        Downloads the given file from S3 into the local /tmp directory (Lambda-compatible).
        Returns the full local path if successful, otherwise None.
        """
        try:
            os.makedirs(local_dir, exist_ok=True)
            local_path = os.path.join(local_dir, os.path.basename(file_name))
            self.client.download_file(self.bucket, file_name, local_path)
            return local_path
        except Exception as e:
            print(f"[S3Client] Error downloading {file_name}: {str(e)}")
            return None

    def upload_document(self, local_path: str, s3_key: str) -> bool:
        """
        Uploads a file from local_path to the specified S3 key.
        Returns True on success, False otherwise.
        """
        try:
            self.client.upload_file(local_path, self.bucket, s3_key)
            return True
        except Exception as e:
            print(f"[S3Client] Error uploading {local_path} to S3: {str(e)}")
            return False

# Singleton instance
s3_client = S3Client()
