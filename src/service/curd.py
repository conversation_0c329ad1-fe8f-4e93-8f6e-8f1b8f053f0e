# src > service > curd.py

from src.server.db import get_db_connection
from psycopg2.extras import <PERSON>son
import hashlib

def insert_extracted_data(job_id, result):
    conn, cursor = get_db_connection()
    if not conn or not cursor:
        return None

    try:
        if isinstance(job_id, str):
            numeric_job_id = abs(hash(job_id)) % (10**8)
        else:
            numeric_job_id = job_id
            
        cursor.execute("""
            INSERT INTO extracted_data (job_id, data)
            VALUES (%s, %s)
            RETURNING id;
        """, (numeric_job_id, Json(result)))
        extracted_id = cursor.fetchone()[0]
        conn.commit()
        return extracted_id
    except Exception as e:
        print(f"❌ Insert into extracted_data failed: {e}")
        return None
    finally:
        cursor.close()
        conn.close()


def insert_extracted_data_file_link(extracted_data_id, job_id, file_name, data):
    conn, cursor = get_db_connection()
    if not conn or not cursor:
        return

    try:
        if isinstance(job_id, str):
            numeric_job_id = abs(hash(job_id)) % (10**8)
        else:
            numeric_job_id = job_id
            
        cursor.execute("""
            INSERT INTO extracted_data_files (extracted_data_id, job_id, file_name, data)
            VALUES (%s, %s, %s, %s)
        """, (extracted_data_id, numeric_job_id, file_name, Json(data)))
        conn.commit()
    except Exception as e:
        print(f"❌ Insert into extracted_data_files failed: {e}")
    finally:
        cursor.close()
        conn.close()