import re

def extract_address_parts(address, country_code="AU"):
    address = re.sub(r'(?i)\bC/-\b', '', address).strip()
    
    # Common flat/unit pattern across countries
    flatunit_pattern = r'\b(?:Unit|Suite|Level|Flat|Apartment|Apt|Bldg|Building|Block|Tower|Floor|Room|Ofc|Office)\s*[\w\-]+'
    flatunit_matches = re.findall(flatunit_pattern, address, re.IGNORECASE)
    flatunit = ", ".join(flatunit_matches) if flatunit_matches else ""

    # Get remainder after flatunit (for door number search)
    flatunit_end = 0
    if flatunit_matches:
        last_match = list(re.finditer(flatunit_pattern, address, re.IGNORECASE))[-1]
        flatunit_end = last_match.end()
    remainder = address[flatunit_end:]

    # Country-specific logic
    if country_code.upper() == "AU" or country_code.upper() == "NZ":
        # Australia and New Zealand
        street_number_match = re.search(r'\b\d+[A-Z]?(-\d+)?\b', remainder)
    elif country_code.upper() == "US":
        # US
        street_number_match = re.search(r'^\s*\d+[A-Z]?(-\d+)?\b', address)
    elif country_code.upper() == "UK":
        # UK
        street_number_match = re.search(r'\b\d+[A-Z]?(-\d+)?\b', remainder)
    elif country_code.upper() == "EU":
        # European countries (generic for many countries)
        street_number_match = re.search(r'\b\d+[A-Z]?(-\d+)?\b', remainder)
    elif country_code.upper() == "AE":  # Example for Arab region (UAE)
        # UAE addresses
        street_number_match = re.search(r'\b\d+[A-Z]?(-\d+)?\b', remainder)
    elif country_code.upper() == "SA":  # Saudi Arabia
        # Saudi Arabia addresses
        street_number_match = re.search(r'\b\d+[A-Z]?(-\d+)?\b', remainder)
    elif country_code.upper() == "EG":  # Egypt
        # Egypt addresses
        street_number_match = re.search(r'\b\d+[A-Z]?(-\d+)?\b', remainder)
    else:  # Generic fallback
        street_number_match = re.search(r'\b\d+[A-Z]?(-\d+)?\b', address)

    street_number = street_number_match.group(0) if street_number_match else ""

    return {
        "flatunit": flatunit.strip(),
        "street_number": street_number.strip()    
    }


# === Main Execution ===
def update_json_with_address_parts(json_data):
    # Handle transferors
    for person in json_data.get("transferors", []):
        address_parts = extract_address_parts(person["address"]["address"])
        person["address"]["flatunit"] = address_parts["flatunit"]
        person["address"]["street_number"] = address_parts["street_number"]

    # Handle transferees or transferee
    transferees = json_data.get("transferees") or json_data.get("transferee") or []
    for person in transferees:
        address_parts = extract_address_parts(person["address"]["address"])
        person["address"]["flatunit"] = address_parts["flatunit"]
        person["address"]["street_number"] = address_parts["street_number"]

    # Set back to standardized plural key
    json_data["transferees"] = transferees

    # Handle property address
    address_parts = extract_address_parts(json_data["title_and_transaction"]["property_address"]["address"])
    json_data["title_and_transaction"]["property_address"]["flatunit"] = address_parts["flatunit"]
    json_data["title_and_transaction"]["property_address"]["street_number"] = address_parts["street_number"]

    return json_data


def add_default_values(json_data):
    json_data["handwritten_elements"] = 'NO'
    transferees = json_data.get("transferees") or json_data.get("transferee") or []
    json_data["transferees"] = transferees  # Normalize to plural
    return json_data
