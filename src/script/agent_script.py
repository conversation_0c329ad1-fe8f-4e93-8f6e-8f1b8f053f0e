# src/script/agent_script.py
import os
import pdfplumber
import pytesseract
from pdf2image import convert_from_path
import json
import tempfile
import logging
from typing import Dict, Any, Optional
from src.utils import update_json_with_address_parts
from src.prompt.SEW_prompt import get_system_prompt 
from src.prompt.YVW_prompt import get_yvw_system_prompt
from src.server.s3 import s3_client
from src.server.bedrock import bedrockInovke

logger = logging.getLogger(__name__)

class PDFProcessor:
    @staticmethod
    def extract_text(file_path: str) -> str:
        """Extract text from PDF using pdfplumber with OCR fallback."""
        text = ""
        try:
            with pdfplumber.open(file_path) as pdf:
                text = "\n".join(page.extract_text() or "" for page in pdf.pages)
            
            if len(text.strip()) < 100:
                text = PDFProcessor._fallback_ocr(file_path)
                
        except Exception as e:
            logger.error(f"PDF extraction failed: {str(e)}")
            text = PDFProcessor._fallback_ocr(file_path)
            
        return text

    @staticmethod
    def _fallback_ocr(file_path: str) -> str:
        """Fallback to OCR when text extraction fails."""
        try:
            with tempfile.TemporaryDirectory() as temp_dir:
                images = convert_from_path(file_path, dpi=300, output_folder=temp_dir)
                return "\n".join(pytesseract.image_to_string(img) for img in images)
        except Exception as e:
            logger.error(f"OCR fallback failed: {str(e)}")
            return ""

class AgentProcessor:
    @staticmethod
    def process_with_bedrock(text: str, job_type: str) -> Dict:
        """Process text with Bedrock model."""
        # Use original prompt functions
        prompt = get_yvw_system_prompt() if job_type == "YVW" else get_system_prompt()
        user_text = f"DOCUMENT TEXT:\n{text}\n\nGENERATE JSON:" 

        # total_tokens = 0
        # total_tokens += log_token_details(prompt, "System Prompt")
        # total_tokens += log_token_details(user_text, "User Message")
        # logger.info(f"[TokenInfo] TOTAL TOKENS = {total_tokens}")
        
        payload = {
            "modelId": "mistral.mistral-large-2402-v1:0",
            "body": {
                "messages": [
                    {"role": "system", "content": prompt},
                    {"role": "user", "content": f"DOCUMENT TEXT:\n{text}\n\nGENERATE JSON:"}
                ]
            }
        }
        

        # You may want to guard against overflow
        # if total_tokens > 29000:
        #     logger.warning("⚠️ Too many tokens! Consider splitting the input.")
        try:
            response = bedrockInovke(payload["modelId"], payload)
            if not isinstance(response, str):
                logger.error("Invalid Bedrock response type")
                return {}
                
            return json.loads(response)
        except json.JSONDecodeError as e:
            logger.error(f"JSON decode error: {str(e)}")
            return {}
        except Exception as e:
            logger.error(f"Bedrock processing failed: {str(e)}")
            return {}

def run_agent(job_id: str, job_type: str, file_path: str) -> Dict[str, Any]:
    """Main agent function to process a document."""
    logger.info(f"Processing {file_path} for job {job_id}")
    
    try:
        if not os.path.exists(file_path):
            file_path = s3_client.get_document(file_path)
            if not file_path:
                logger.error(f"File not found: {file_path}")
                return {}

        text = PDFProcessor.extract_text(file_path)
        if not text:
            logger.error("No text extracted from document")
            return {}

        result = AgentProcessor.process_with_bedrock(text, job_type)
        if not result:
            logger.error("Bedrock processing returned empty result")
            return {}

        return update_json_with_address_parts(result)
    except Exception as e:
        logger.error(f"Agent processing failed: {str(e)}")
        return {}






































# # src/script/agent_script.py
# import os
# import pdfplumber
# import fitz  # PyMuPDF - replaces pdf2image
# import requests  # For API calls - replaces pytesseract
# import json
# import tempfile
# import logging
# import time
# import random
# from typing import Dict, Any, Optional
# from src.utils import update_json_with_address_parts
# from src.prompt.SEW_prompt import get_system_prompt 
# from src.prompt.YVW_prompt import get_yvw_system_prompt
# from src.server.s3 import s3_client
# from src.server.bedrock import bedrockInovke
# from src.script.token_utils import log_token_details

# logger = logging.getLogger(__name__)

# class PDFProcessor:
#     @staticmethod
#     def extract_text(file_path: str) -> str:
#         """Extract text from PDF using pdfplumber with OCR fallback."""
#         text = ""
#         try:
#             with pdfplumber.open(file_path) as pdf:
#                 text = "\n".join(page.extract_text() or "" for page in pdf.pages)
            
#             if len(text.strip()) < 100:
#                 text = PDFProcessor._fallback_ocr(file_path)
                
#         except Exception as e:
#             logger.error(f"PDF extraction failed: {str(e)}")
#             text = PDFProcessor._fallback_ocr(file_path)
            
#         return text

#     @staticmethod
#     def _fallback_ocr(file_path: str) -> str:
#         """Fallback to OCR using external API when text extraction fails."""
#         try:
#             return PDFProcessor._ocr_with_api(file_path)
#         except Exception as e:
#             logger.error(f"OCR fallback failed: {str(e)}")
#             return ""

#     @staticmethod
#     def _ocr_with_api(file_path: str) -> str:
#         """OCR using OCR.space API (no local dependencies)."""
#         api_key = os.environ.get('OCRSPACE_API_KEY', 'K87899142388957')  # Free key
#         url = "https://api.ocr.space/parse/image"
        
#         try:
#             # Convert PDF to images using PyMuPDF
#             doc = fitz.open(file_path)
#             text = ""
            
#             # Process each page (limit to 5 for free tier)
#             for page_num in range(min(5, len(doc))):
#                 page = doc[page_num]
#                 pix = page.get_pixmap(matrix=fitz.Matrix(1.5, 1.5))
#                 img_data = pix.tobytes("png")
                
#                 # Send to OCR API
#                 files = {'file': ('page.png', img_data, 'image/png')}
#                 data = {
#                     'apikey': api_key,
#                     'language': 'eng',
#                     'OCREngine': '2',
#                     'scale': 'true',
#                     'detectOrientation': 'true'
#                 }
                
#                 response = requests.post(url, files=files, data=data, timeout=30)
#                 result = response.json()
                
#                 if result.get('OCRExitCode') == 1:
#                     parsed_results = result.get('ParsedResults', [])
#                     if parsed_results:
#                         text += parsed_results[0].get('ParsedText', '') + "\n"
            
#             doc.close()
#             return text
            
#         except Exception as e:
#             logger.error(f"External OCR API failed: {str(e)}")
#             return ""

# # Keep your existing AgentProcessor and run_agent functions unchanged
# class AgentProcessor:
#     @staticmethod
#     def process_with_bedrock(text: str, job_type: str) -> Dict:
#         """Process text with Bedrock model."""
#         # Use original prompt functions
#         prompt = get_yvw_system_prompt() if job_type == "YVW" else get_system_prompt()
#         user_text = f"DOCUMENT TEXT:\n{text}\n\nGENERATE JSON:" 

#         total_tokens = 0
#         total_tokens += log_token_details(prompt, "System Prompt")
#         total_tokens += log_token_details(user_text, "User Message")
#         logger.info(f"[TokenInfo] TOTAL TOKENS = {total_tokens}")
        
#         payload = {
#             "modelId": "mistral.mistral-large-2402-v1:0",
#             "body": {
#                 "messages": [
#                     {"role": "system", "content": prompt},
#                     {"role": "user", "content": f"DOCUMENT TEXT:\n{text}\n\nGENERATE JSON:"}
#                 ]
#             }
#         }
        
#         if total_tokens > 29000:
#             logger.warning("⚠️ Too many tokens! Consider splitting the input.")
#         try:
#             response = bedrockInovke(payload["modelId"], payload)
#             time.sleep(0.3 + random.uniform(0, 0.1))
#             if not isinstance(response, str):
#                 logger.error("Invalid Bedrock response type")
#                 return {}
                
#             return json.loads(response)
#         except json.JSONDecodeError as e:
#             logger.error(f"JSON decode error: {str(e)}")
#             return {}
#         except Exception as e:
#             logger.error(f"Bedrock processing failed: {str(e)}")
#             return {}

# def run_agent(job_id: str, job_type: str, file_path: str) -> Dict[str, Any]:
#     """Main agent function to process a document."""
#     logger.info(f"Processing {file_path} for job {job_id}")
    
#     try:
#         if not os.path.exists(file_path):
#             file_path = s3_client.get_document(file_path)
#             if not file_path:
#                 logger.error(f"File not found: {file_path}")
#                 return {}

#         text = PDFProcessor.extract_text(file_path)
#         if not text:
#             logger.error("No text extracted from document")
#             return {}

#         result = AgentProcessor.process_with_bedrock(text, job_type)
#         if not result:
#             logger.error("Bedrock processing returned empty result")
#             return {}

#         return update_json_with_address_parts(result)
#     except Exception as e:
#         logger.error(f"Agent processing failed: {str(e)}")
#         return {}