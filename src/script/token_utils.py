import tiktoken
import logging

logger = logging.getLogger(__name__)

def log_token_details(text: str, label: str = "Text", model: str = "gpt-3.5-turbo") -> int:
    try:
        encoding = tiktoken.encoding_for_model(model)
    except KeyError:
        encoding = tiktoken.get_encoding("cl100k_base")
    
    tokens = encoding.encode(text)
    logger.info(f"[TokenInfo] {label} — Token Count: {len(tokens)}")
    logger.info(f"[TokenInfo] {label} — First 200 chars: {text[:200]}")
    logger.info(f"[TokenInfo] {label} — First 20 tokens: {tokens[:20]}")
    return len(tokens)
