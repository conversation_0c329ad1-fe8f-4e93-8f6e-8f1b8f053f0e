import os
import json
import tempfile
import zipfile
import logging
from typing import Dict, Optional, List
from concurrent.futures import ThreadPoolExecutor, as_completed
import pytesseract
import boto3

# Local imports
from src.service.curd import insert_extracted_data, insert_extracted_data_file_link
from src.script.agent_script import run_agent
from src.server.test import modelCheck

# Constants
BATCH_SIZE = 50
S3_BUCKET = os.environ["S3_BUCKET_NAME"]  # Ensure this is set in Lambda

# S3 client
s3_client = boto3.client("s3")

# Logging
logger = logging.getLogger()
logger.setLevel(logging.INFO)

# File Processor
class FileProcessor:
    @staticmethod
    def extract_zip(zip_path: str, extract_to: str) -> List[str]:
        extracted_files = []
        with zipfile.ZipFile(zip_path, "r") as zip_ref:
            zip_ref.extractall(extract_to)
            extracted_files = [
                os.path.join(extract_to, f.filename)
                for f in zip_ref.infolist()
                if not f.is_dir() and f.filename.lower().endswith(".pdf")
            ]
        return extracted_files

    @staticmethod
    def download_from_s3(file_name: str, local_dir: str) -> Optional[str]:
        local_path = os.path.join(local_dir, os.path.basename(file_name))
        try:
            logger.info(f"Downloading from S3: bucket={S3_BUCKET}, key={file_name}")
            s3_client.download_file(S3_BUCKET, file_name, local_path)
            return local_path
        except Exception as e:
            logger.error(f"S3 download failed: {str(e)} - key: {file_name}")
            return None        

    @staticmethod
    def cleanup_file(file_path: str):
        if file_path and os.path.exists(file_path):
            os.remove(file_path)

# Process single file
def process_single_file(job_id: str, job_type: str, file_name: str, temp_dir: str) -> Optional[Dict]:
    try:
        local_path = FileProcessor.download_from_s3(file_name, temp_dir)
        logger.info(f"Downloaded file saved to: {local_path}")
        if not local_path:
            return None

        if file_name.lower().endswith(".zip"):
            extracted_files = FileProcessor.extract_zip(local_path, temp_dir)
            FileProcessor.cleanup_file(local_path)

            zip_results = {}
            for extracted_file in extracted_files:
                result = run_agent(job_id, job_type, extracted_file)
                if result:
                    zip_results[os.path.basename(extracted_file)] = result
                FileProcessor.cleanup_file(extracted_file)

            return zip_results if zip_results else None
        else:
            result = run_agent(job_id, job_type, local_path)
            FileProcessor.cleanup_file(local_path)
            return result if result else None
    except Exception as e:
        logger.error(f"Processing error for {file_name}: {str(e)}")
        return None

# Run-agent logic
def handle_run_agent(data):
    job_id = data.get("job_id")
    job_type = data.get("job_type")
    file_names = data.get("file_names", [])

    if not job_id or not file_names or not job_type:
        logger.error("Missing job_id, file_names, or job_type.")
        return

    results = {}
    failed_files = []
    processed_files = []

    with tempfile.TemporaryDirectory() as temp_dir:
        for batch_start in range(0, len(file_names), BATCH_SIZE):
            batch = file_names[batch_start:batch_start + BATCH_SIZE]
            with ThreadPoolExecutor() as executor:
                futures = {
                    executor.submit(process_single_file, job_id, job_type, fname, temp_dir): fname
                    for fname in batch
                }

                for future in as_completed(futures):
                    file_name = futures[future]
                    print("✅file_name::", file_name)
                    try:
                        file_result = future.result()
                        if file_result:
                            results[file_name] = file_result
                            print("file_result:: fromm handle_run_agent ", file_result)
                            processed_files.append(file_name)
                        else:
                            failed_files.append(file_name)
                    except Exception as e:
                        logger.error(f"Error processing file {file_name}: {str(e)}")
                        failed_files.append(file_name)

    if results:
        extracted_id = insert_extracted_data(job_id, results)
        if extracted_id:
            for file_name, file_result in results.items():
                insert_extracted_data_file_link(extracted_id, job_id, file_name, file_result)
        else:
            logger.error("Failed to insert extracted data into DB")

    logger.info(f"Run-agent finished: success={len(processed_files)}, failed={len(failed_files)}")

# Lambda handler
def handler(event, context):
    # event = {'Records': [{'messageId': '34311176-54c5-423e-895d-f046696008dc', 'receiptHandle': 'AQEBAON+MTCscqz8I3LSfYNtme42DyNm0WTwENoJEzZ6yuGegmsbZzbUm5j3vUbtel/ZszSaaM0wVIahitN7git7mvZOwoFiaMM3sNnCrcxI/bb3/pA1qskOW76sRJohYw9Qqh9LBKq1dlko+DMXek9J0ZFkxFlrK2ko9wt6SKEEhq7MiHmXOopGtNnBPfalQvMBb6D+/0W6IObZl6yO9Vjam0XzGkzZz8kMpd/LiwWhhv4WmOqIF20CebE+FrMaqBScNVggjvOnMXqiJaAPBu0SIuBqkF6pQnroM7Cq8FgRoBtwtnW6SoSHM6lqMPIO/VKsYNphEEd9G6ypDTUyAHUqQUtVO7CyV5zgh6WIKTNkuquiv2yi2ex25VW81w5Pg7MFDiUORt3oEulh1vu87AbKSsgxIMpUibTF2LF6e2qdpResmvVuTCDStDl+589S1RRL', 'body': '{"path": "/run-agent", "payload": {"job_id": "1-1", "job_type": "YVW", "file_names": ["sample_noa_test_doc.pdf"]}}', 'attributes': {'ApproximateReceiveCount': '1', 'AWSTraceHeader': 'Root=1-685a4cfd-71462fb34533ca3abbe7ad4c;Parent=1df8a778bf91a7cc;Sampled=0;Lineage=1:3ed75329:0', 'SentTimestamp': '1750748414167', 'SenderId': 'AROASBS6IXB26H2YXOO7P:sun-fileproc-micro-stack-file-batch-processor', 'ApproximateFirstReceiveTimestamp': '1750748414172'}, 'messageAttributes': {}, 'md5OfBody': '9ed9fe025b764f534af2a5bd34a7e967', 'eventSource': 'aws:sqs', 'eventSourceARN': 'arn:aws:sqs:ap-south-1:140857882741:sun-fileproc-micro-stack-file-processing-queue', 'awsRegion': 'ap-south-1'}]}
    print("🚀 🚀 event::", event)
    for record in event['Records']:
        try:
            message = json.loads(record["body"])
            path = message.get("path")
            payload = message.get("payload", {})

            if path == "/run-agent":
                logger.info("Running /run-agent handler")
                handle_run_agent(payload)

            elif path == "/model-list":
                logger.info("Running /model-list handler")
                models = modelCheck()
                logger.info(f"Available models: {models}")

            elif path == "/":
                logger.info("Running health check")

            else:
                logger.warning(f"Unknown path in event: {path}")

        except Exception as e:
            logger.error(f"Exception while processing SQS record: {str(e)}")
